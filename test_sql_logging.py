#!/usr/bin/env python3
"""
测试 SQL 日志记录功能
"""

import sys
import os
import logging

# 添加项目根目录到系统路径
sys.path.append(os.getcwd())

from server.utils.pg_tool import query_data, connect_db, log_sql_execution
from server.utils.logger import setup_logging

def test_sql_logging():
    """测试 SQL 日志记录功能"""
    
    # 设置日志
    setup_logging()
    logger = logging.getLogger(__name__)
    
    print("=" * 60)
    print("测试 SQL 日志记录功能")
    print("=" * 60)
    
    # 测试1: 测试直接调用 log_sql_execution 函数
    print("\n1. 测试直接调用 log_sql_execution 函数")
    test_sql = "SELECT * FROM frame_analysis WHERE camera_id = %s LIMIT 5"
    test_params = ['4052']
    test_execution_time = 0.123
    
    log_sql_execution(test_sql, test_params, test_execution_time)
    print("✓ 直接调用 log_sql_execution 完成")
    
    # 测试2: 测试通过 query_data 函数的 SQL 日志记录
    print("\n2. 测试通过 query_data 函数的 SQL 日志记录")
    try:
        result = query_data(
            table="frame_analysis_latest",
            conditions={"camera_id": "4052"},
            fields=["camera_id", "timestamp", "coverage_level"],
            order_by="timestamp DESC",
            limit=3
        )
        print(f"✓ query_data 执行完成，返回 {len(result)} 条记录")
        if result:
            print(f"  示例记录: {result[0]}")
    except Exception as e:
        print(f"✗ query_data 执行失败: {e}")
    
    # 测试3: 测试错误情况的 SQL 日志记录
    print("\n3. 测试错误情况的 SQL 日志记录")
    try:
        result = query_data(
            table="non_existent_table",
            conditions={"id": 1},
            limit=1
        )
        print(f"✗ 预期应该失败，但返回了: {result}")
    except Exception as e:
        print(f"✓ 正确捕获了错误，SQL 错误日志应该已记录")
    
    # 测试4: 测试慢查询日志记录
    print("\n4. 测试慢查询模拟（通过 log_sql_execution）")
    slow_sql = "SELECT COUNT(*) FROM frame_analysis"
    slow_execution_time = 2.5  # 超过默认的 1.0 秒阈值
    log_sql_execution(slow_sql, [], slow_execution_time)
    print("✓ 慢查询日志记录完成")
    
    print("\n" + "=" * 60)
    print("SQL 日志记录功能测试完成")
    print("请查看日志文件以确认 SQL 语句已被正确记录")
    print("日志文件位置: logs/[年]/[月]/[日]/video_processing.log")
    print("=" * 60)

if __name__ == "__main__":
    test_sql_logging()
