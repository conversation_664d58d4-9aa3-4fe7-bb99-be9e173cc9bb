"""
数据库连接池优化模块
用于减少数据库连接开销，提升查询性能
"""
import psycopg2
from psycopg2 import pool
import threading
import logging
import time
from contextlib import contextmanager
from config_file import config

def log_sql_execution(sql, params=None, execution_time=None, error=None):
    """
    记录 SQL 执行日志

    Args:
        sql (str): 执行的 SQL 语句
        params (list/tuple): SQL 参数
        execution_time (float): 执行时间（秒）
        error (Exception): 执行错误（如果有）
    """
    try:
        # 获取 SQL 日志配置
        sql_log_config = config.env.get('storage', {}).get('logging', {}).get('sql_logging', {})

        # 检查是否启用 SQL 日志
        if not sql_log_config.get('enabled', True):
            return

        log_level = sql_log_config.get('level', 'INFO').upper()
        slow_threshold = sql_log_config.get('slow_query_threshold', 1.0)
        log_params = sql_log_config.get('log_parameters', True)

        # 格式化 SQL 语句（移除多余的空白字符）
        formatted_sql = ' '.join(sql.split())

        # 构建日志消息
        if error:
            # 错误日志
            log_msg = f"SQL执行失败: {formatted_sql}"
            if log_params and params:
                log_msg += f" | 参数: {params}"
            if execution_time is not None:
                log_msg += f" | 执行时间: {execution_time:.3f}s"
            log_msg += f" | 错误: {str(error)}"
            logging.error(log_msg)
        else:
            # 成功执行日志
            log_msg = f"SQL执行: {formatted_sql}"
            if log_params and params:
                log_msg += f" | 参数: {params}"
            if execution_time is not None:
                log_msg += f" | 执行时间: {execution_time:.3f}s"

                # 根据执行时间和配置决定日志级别
                if execution_time > slow_threshold:
                    logging.warning(f"慢查询: {log_msg}")
                elif log_level == 'DEBUG':
                    logging.debug(log_msg)
                elif log_level == 'INFO':
                    logging.info(log_msg)
            else:
                # 没有执行时间信息时，使用配置的日志级别
                if log_level == 'DEBUG':
                    logging.debug(log_msg)
                elif log_level == 'INFO':
                    logging.info(log_msg)

    except Exception as log_error:
        # 日志记录本身出错时，不应该影响主要功能
        logging.error(f"SQL日志记录失败: {str(log_error)}")

class DatabasePool:
    """数据库连接池单例类"""
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super(DatabasePool, cls).__new__(cls)
                    cls._instance._initialized = False
        return cls._instance
    
    def __init__(self):
        if self._initialized:
            return
        
        try:
            # 从配置文件获取数据库连接信息
            db_config = config.env['database']['postgres']
            
            # 创建连接池
            self._pool = psycopg2.pool.ThreadedConnectionPool(
                minconn=5,      # 最小连接数
                maxconn=20,     # 最大连接数
                host=db_config['host'],
                port=db_config['port'],
                database=db_config['dbname'],
                user=db_config['user'],
                password=db_config['password'],
                # 连接超时设置
                connect_timeout=10,
                # 其他优化参数
                options='-c statement_timeout=30000'  # 30秒查询超时
            )
            self._initialized = True
            logging.info("数据库连接池初始化成功")
            
        except Exception as e:
            logging.error(f"数据库连接池初始化失败: {e}")
            self._pool = None
            self._initialized = False
            raise
    
    @contextmanager
    def get_connection(self):
        """获取数据库连接的上下文管理器"""
        if not self._pool:
            raise Exception("数据库连接池未初始化")
        
        conn = None
        try:
            # 从连接池获取连接
            conn = self._pool.getconn()
            if conn:
                yield conn
            else:
                raise Exception("无法从连接池获取连接")
        except Exception as e:
            if conn:
                # 如果连接有问题，回滚事务
                try:
                    conn.rollback()
                except:
                    pass
            logging.error(f"数据库连接错误: {e}")
            raise
        finally:
            if conn:
                # 将连接归还给连接池
                self._pool.putconn(conn)
    
    def close_all_connections(self):
        """关闭所有连接"""
        if self._pool:
            self._pool.closeall()
            logging.info("所有数据库连接已关闭")

# 创建全局连接池实例
db_pool = DatabasePool()

def get_db_connection():
    """获取数据库连接（兼容原有代码的函数）"""
    return db_pool.get_connection()

# 优化后的查询函数
def query_data_optimized(table, conditions=None, fields=None, order_by=None, limit=None):
    """
    使用连接池的优化查询函数

    Args:
        table (str): 表名
        conditions (dict): 查询条件
        fields (list): 查询字段
        order_by (str): 排序条件
        limit (int): 限制返回记录数

    Returns:
        list: 查询结果列表
    """
    start_time = time.time()
    query_sql = ""
    condition_values = []

    try:
        with db_pool.get_connection() as conn:
            cur = conn.cursor()

            # 构建查询字段
            if fields:
                fields_str = ', '.join(fields)
            else:
                fields_str = '*'

            # 构建查询条件
            query_sql = f"SELECT {fields_str} FROM {table}"

            if conditions:
                condition_str = ' AND '.join([f"{key} = %s" for key in conditions.keys()])
                condition_values = list(conditions.values())
                query_sql += f" WHERE {condition_str}"

            # 添加排序
            if order_by:
                query_sql += f" ORDER BY {order_by}"

            # 添加结果数量限制
            if limit:
                query_sql += f" LIMIT {limit}"

            cur.execute(query_sql, condition_values)
            result = cur.fetchall()

            # 记录执行时间和日志
            execution_time = time.time() - start_time
            log_sql_execution(query_sql, condition_values, execution_time)

            return result

    except Exception as e:
        # 记录错误日志
        execution_time = time.time() - start_time
        log_sql_execution(query_sql, condition_values, execution_time, e)
        logging.error(f"查询数据错误: {e}")
        return []

def batch_query_by_camera_ids(camera_ids, fields=None):
    """
    批量查询多个camera_id的数据（同步函数，供run_in_threadpool使用）
    专门用于优化 query_by_cameras 接口

    Args:
        camera_ids (list): 摄像头ID列表
        fields (list): 查询字段列表

    Returns:
        list: 查询结果列表

    Raises:
        Exception: 数据库查询错误
    """
    if not camera_ids:
        return []

    start_time = time.time()
    query_sql = ""

    try:
        with db_pool.get_connection() as conn:
            cur = conn.cursor()

            # 默认查询字段
            if not fields:
                fields = [
                    "camera_id", "video_id", "frame_number", "timestamp", "frame_path",
                    "coverage_rate", "coverage_level", "alarm_status", "analysis_detail",
                    "is_abnormal", "do_value", "mlss_value", "adjustment_suggestion",
                    "is_read", "failure_reasons_type", "failure_reasons_number",
                    "alarmtype"
                ]

            fields_str = ', '.join(fields)
            placeholders = ', '.join(['%s'] * len(camera_ids))

            query_sql = f"""
                SELECT {fields_str}
                FROM frame_analysis_latest
                WHERE camera_id IN ({placeholders})
                ORDER BY camera_id, timestamp DESC
            """

            cur.execute(query_sql, camera_ids)
            results = cur.fetchall()

            # 记录执行时间和日志
            execution_time = time.time() - start_time
            log_sql_execution(query_sql, camera_ids, execution_time)

            return results

    except Exception as e:
        # 记录错误日志
        execution_time = time.time() - start_time
        log_sql_execution(query_sql, camera_ids, execution_time, e)
        raise  # 重新抛出异常，保持原有的错误处理逻辑
