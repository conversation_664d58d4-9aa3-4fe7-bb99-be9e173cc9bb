# SQL 日志记录功能说明

## 概述

为了便于调试和问题排查，我们在所有数据库操作中添加了 SQL 语句日志记录功能。该功能可以记录：

- 执行的 SQL 语句
- SQL 参数
- 执行时间
- 错误信息（如果有）

## 配置说明

### 配置文件位置
`configs/env.yaml`

### 配置项说明

```yaml
storage:
  logging:
    sql_logging:
      # 是否启用 SQL 语句日志记录
      enabled: true
      
      # SQL 日志级别
      # DEBUG: 记录所有 SQL 语句和参数的详细信息
      # INFO: 记录 SQL 语句和基本执行信息
      # WARNING: 只记录执行时间较长的 SQL 语句
      level: "INFO"
      
      # 慢查询阈值（秒）
      # 执行时间超过此阈值的 SQL 语句将被记录为 WARNING 级别
      slow_query_threshold: 1.0
      
      # 是否记录查询参数
      # true: 记录 SQL 语句的绑定参数
      # false: 只记录 SQL 语句，不记录参数（提高安全性）
      log_parameters: true
```

## 功能特性

### 1. 自动 SQL 日志记录
- 所有通过 `pg_tool.py` 和 `db_pool.py` 执行的 SQL 语句都会自动记录日志
- 包括查询、插入、更新、删除操作

### 2. 执行时间监控
- 记录每个 SQL 语句的执行时间
- 自动识别慢查询并标记为 WARNING 级别

### 3. 错误日志记录
- 当 SQL 执行失败时，记录详细的错误信息
- 包括 SQL 语句、参数和错误原因

### 4. 可配置的日志级别
- **DEBUG**: 记录所有 SQL 详细信息
- **INFO**: 记录基本 SQL 信息
- **WARNING**: 只记录慢查询和错误

## 日志格式示例

### 正常查询日志
```
2024-03-21 10:30:15 - [INFO   ] - SQL执行: SELECT camera_id, timestamp, coverage_level FROM frame_analysis_latest WHERE camera_id = %s ORDER BY timestamp DESC LIMIT 3 | 参数: ['4052'] | 执行时间: 0.045s
```

### 慢查询日志
```
2024-03-21 10:30:20 - [WARNING] - 慢查询: SQL执行: SELECT COUNT(*) FROM frame_analysis | 参数: [] | 执行时间: 2.150s
```

### 错误日志
```
2024-03-21 10:30:25 - [ERROR  ] - SQL执行失败: SELECT * FROM non_existent_table WHERE id = %s | 参数: [1] | 执行时间: 0.012s | 错误: relation "non_existent_table" does not exist
```

## 已修改的文件

### 1. 配置文件
- `configs/env.yaml`: 添加了 SQL 日志配置项

### 2. 数据库工具文件
- `server/utils/pg_tool.py`: 
  - 添加了 `log_sql_execution()` 函数
  - 修改了所有数据库操作函数以支持 SQL 日志记录
- `server/utils/db_pool.py`:
  - 添加了 `log_sql_execution()` 函数
  - 修改了连接池相关的查询函数

### 3. API 文件
- `server/cam/api.py`:
  - 导入了 SQL 日志记录函数
  - 修改了部分直接 SQL 执行的地方以支持日志记录

## 使用方法

### 1. 启用/禁用 SQL 日志
修改 `configs/env.yaml` 中的 `enabled` 配置：
```yaml
sql_logging:
  enabled: true  # 启用
  # enabled: false  # 禁用
```

### 2. 调整日志级别
```yaml
sql_logging:
  level: "DEBUG"  # 记录所有详细信息
  # level: "INFO"   # 记录基本信息
  # level: "WARNING"  # 只记录慢查询和错误
```

### 3. 调整慢查询阈值
```yaml
sql_logging:
  slow_query_threshold: 0.5  # 0.5秒
  # slow_query_threshold: 1.0  # 1秒（默认）
  # slow_query_threshold: 2.0  # 2秒
```

## 测试

运行测试脚本验证功能：
```bash
python test_sql_logging.py
```

## 注意事项

### 1. 性能影响
- SQL 日志记录会有轻微的性能开销
- 在生产环境中，建议设置合适的日志级别

### 2. 安全考虑
- 如果 SQL 参数包含敏感信息，可以设置 `log_parameters: false`
- 日志文件应该有适当的访问权限控制

### 3. 存储空间
- SQL 日志会增加日志文件大小
- 建议定期清理旧的日志文件

## 故障排查

### 1. 查看 SQL 执行情况
当 API 接口出现问题时，可以通过查看日志文件中的 SQL 执行记录来定位问题：

```bash
# 查看最新的日志
tail -f logs/$(date +%Y)/$(date +%m)/$(date +%d)/video_processing.log | grep "SQL执行"

# 查看错误的 SQL
grep "SQL执行失败" logs/$(date +%Y)/$(date +%m)/$(date +%d)/video_processing.log

# 查看慢查询
grep "慢查询" logs/$(date +%Y)/$(date +%m)/$(date +%d)/video_processing.log
```

### 2. 分析执行时间
通过日志中的执行时间信息，可以识别性能瓶颈：
- 执行时间超过 1 秒的查询会被标记为慢查询
- 可以根据实际情况调整 `slow_query_threshold` 配置

这样，当数据库操作出现问题时，开发人员可以快速查看具体执行了哪些 SQL 语句，以及执行的参数和结果，大大提高了问题排查的效率。
